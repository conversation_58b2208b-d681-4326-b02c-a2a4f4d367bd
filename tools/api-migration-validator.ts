/**
 * API迁移验证工具
 * 基于@mswjs/interceptors和jsondiffpatch的API迁移验证解决方案
 */

import { BaseTool } from '../utils/tool-template';
import { Modal } from '../utils/ui-components';
// import { notificationManager } from '../utils/notification-manager'; // 暂时注释，未使用

// 核心类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
    headers?: Record<string, string>;
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    headerMapping?: Record<string, string>;
    preserveOriginalParams?: boolean;
  };
  mode: 'redirect' | 'parallel';
  diffConfig?: {
    ignoreFields?: string[];
    caseSensitive?: boolean;
    arrayOrderSensitive?: boolean;
    numericTolerance?: number;
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
  };
  responses: {
    old: { status: number; body: any; responseTime: number };
    new: { status: number; body: any; responseTime: number };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

export class ApiMigrationValidatorTool extends BaseTool {
  id = 'api-migration-validator';
  name = 'API迁移验证工具';
  description = '拦截API请求，验证新老接口的兼容性，支持重定向和并行对比两种模式';
  icon = '🔄';
  categories = ['all'];
  version = { major: 1, minor: 0, patch: 0 };

  // 核心组件
  private interceptor: any = null;
  private rules: InterceptRule[] = [];
  private reports: DiffReport[] = [];
  private isIntercepting = false; 

  // 此工具使用外部npm包依赖，在运行时动态检查
  dependencies = [];

  async action(): Promise<void> {
    try {
      // 检查依赖
      if (!await this.checkDependencies()) {
        await this.showDependencyInstallGuide();
        return;
      }

      // 加载保存的规则
      await this.loadRules();

      // 创建主界面
      const modal = this.createMainModal();
      document.body.appendChild(modal);

      // 初始化UI
      this.initializeUI(modal);

    } catch (error) {
      console.error('API迁移验证工具启动失败:', error);
      await this.showNotification('错误', 'API迁移验证工具启动失败');
    }
  }

  /**
   * 检查外部依赖
   */
  private async checkDependencies(): Promise<boolean> {
    try {
      // 检查@mswjs/interceptors
      await import('@mswjs/interceptors');
      // 检查jsondiffpatch
      await import('jsondiffpatch');
      return true;
    } catch (error) {
      console.warn('缺少必要依赖:', error);
      return false;
    }
  }

  /**
   * 显示依赖安装指南
   */
  private async showDependencyInstallGuide(): Promise<void> {
    const modal = new Modal(`
      <div class="dependency-guide">
        <h4>📦 安装必要依赖</h4>
        <p>此工具需要以下npm包：</p>
        <div class="code-block">
          <code>npm install @mswjs/interceptors jsondiffpatch</code>
        </div>
        <p>安装完成后请重新启动工具。</p>
      </div>
    `, {
      title: '缺少依赖',
      size: 'md',
      closable: true
    });

    modal.addFooter(`
      <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove()">了解</button>
    `);

    modal.open();
  }

  /**
   * 创建主界面模态框
   */
  private createMainModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay api-migration-validator-modal';
    modal.innerHTML = `
      <div class="modal-content modal-xl">
        <div class="modal-header">
          <h3 class="modal-title">🔄 API迁移验证工具</h3>
          <div class="header-actions">
            <span class="badge badge-${this.isIntercepting ? 'success' : 'secondary'}" id="status-badge">
              ${this.isIntercepting ? '拦截中' : '已停止'}
            </span>
            <button class="btn btn-sm btn-outline" id="toggle-interceptor">
              ${this.isIntercepting ? '停止拦截' : '开始拦截'}
            </button>
            <button class="modal-close">&times;</button>
          </div>
        </div>
        <div class="modal-body">
          <div class="tabs">
            <ul class="tabs-list">
              <li class="tabs-item">
                <a href="#rules" class="tabs-link active">拦截规则</a>
              </li>
              <li class="tabs-item">
                <a href="#reports" class="tabs-link">对比报告</a>
              </li>
              <li class="tabs-item">
                <a href="#settings" class="tabs-link">设置</a>
              </li>
            </ul>
          </div>
          
          <div class="tabs-content">
            <!-- 拦截规则标签页 -->
            <div id="rules" class="tab-pane active">
              <div class="rules-toolbar">
                <button class="btn btn-primary" id="add-rule-btn">
                  <span>➕</span> 添加规则
                </button>
                <button class="btn btn-outline" id="import-rules-btn">导入规则</button>
                <button class="btn btn-outline" id="export-rules-btn">导出规则</button>
              </div>
              <div class="rules-list" id="rules-list">
                <!-- 规则列表将在这里动态生成 -->
              </div>
            </div>
            
            <!-- 对比报告标签页 -->
            <div id="reports" class="tab-pane">
              <div class="reports-toolbar">
                <button class="btn btn-outline" id="clear-reports-btn">清空报告</button>
                <span class="reports-count">共 <span id="reports-count">0</span> 条报告</span>
              </div>
              <div class="reports-list" id="reports-list">
                <!-- 报告列表将在这里动态生成 -->
              </div>
            </div>
            
            <!-- 设置标签页 -->
            <div id="settings" class="tab-pane">
              <div class="settings-form">
                <div class="form-group">
                  <label class="form-label">最大报告数量</label>
                  <input type="number" class="form-control" id="max-reports" value="100" min="10" max="1000">
                </div>
                <div class="form-group">
                  <label class="form-label">全局忽略模式</label>
                  <textarea class="form-control" id="global-ignore" rows="3" 
                    placeholder="每行一个URL模式，支持正则表达式"></textarea>
                </div>
                <div class="form-check">
                  <input type="checkbox" class="form-check-input" id="auto-start" checked>
                  <label class="form-check-label" for="auto-start">启动时自动开始拦截</label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="close-btn">关闭</button>
        </div>
      </div>
    `;

    return modal;
  }

  /**
   * 初始化UI组件和事件
   */
  private initializeUI(modal: HTMLElement): void {
    // 标签页切换
    this.initializeTabs(modal);

    // 拦截器控制
    this.initializeInterceptorControls(modal);

    // 规则管理
    this.initializeRuleManagement(modal);

    // 报告管理
    this.initializeReportManagement(modal);

    // 设置管理
    this.initializeSettings(modal);

    // 关闭事件
    this.initializeCloseEvents(modal);

    // 渲染初始数据
    this.renderRulesList(modal);
    this.renderReportsList(modal);
  }

  /**
   * 初始化标签页切换
   */
  private initializeTabs(modal: HTMLElement): void {
    const tabLinks = modal.querySelectorAll('.tabs-link');
    const tabPanes = modal.querySelectorAll('.tab-pane');

    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = (e.target as HTMLElement).getAttribute('href')?.substring(1);
        
        // 移除所有active类
        tabLinks.forEach(l => l.classList.remove('active'));
        tabPanes.forEach(p => p.classList.remove('active'));
        
        // 添加active类到当前标签
        (e.target as HTMLElement).classList.add('active');
        const targetPane = modal.querySelector(`#${target}`);
        if (targetPane) {
          targetPane.classList.add('active');
        }
      });
    });
  }

  /**
   * 初始化拦截器控制
   */
  private initializeInterceptorControls(modal: HTMLElement): void {
    const toggleBtn = modal.querySelector('#toggle-interceptor') as HTMLButtonElement;
    const statusBadge = modal.querySelector('#status-badge') as HTMLElement;

    toggleBtn?.addEventListener('click', async () => {
      if (this.isIntercepting) {
        await this.stopInterceptor();
        toggleBtn.textContent = '开始拦截';
        statusBadge.textContent = '已停止';
        statusBadge.className = 'badge badge-secondary';
      } else {
        await this.startInterceptor();
        toggleBtn.textContent = '停止拦截';
        statusBadge.textContent = '拦截中';
        statusBadge.className = 'badge badge-success';
      }
    });
  }

  /**
   * 加载保存的规则
   */
  private async loadRules(): Promise<void> {
    try {
      const savedRules = await this.loadData('rules', []);
      this.rules = savedRules;
    } catch (error) {
      console.error('加载规则失败:', error);
      this.rules = [];
    }
  }

  /**
   * 保存规则到存储
   */
  private async saveRules(): Promise<void> {
    try {
      await this.saveData('rules', this.rules);
    } catch (error) {
      console.error('保存规则失败:', error);
    }
  }

  /**
   * 渲染规则列表
   */
  private renderRulesList(modal: HTMLElement): void {
    const rulesList = modal.querySelector('#rules-list') as HTMLElement;
    if (!rulesList) return;

    if (this.rules.length === 0) {
      rulesList.innerHTML = `
        <div class="empty-state">
          <p>暂无拦截规则</p>
          <p class="text-muted">点击"添加规则"创建第一个API拦截规则</p>
        </div>
      `;
      return;
    }

    rulesList.innerHTML = this.rules.map(rule => `
      <div class="card rule-card" data-rule-id="${rule.id}">
        <div class="card-header">
          <div class="rule-info">
            <h4 class="card-title">${rule.name}</h4>
            <p class="rule-description">${rule.description || '无描述'}</p>
          </div>
          <div class="rule-actions">
            <span class="badge badge-${rule.enabled ? 'success' : 'secondary'}">
              ${rule.enabled ? '启用' : '禁用'}
            </span>
            <span class="badge badge-info">${rule.mode === 'redirect' ? '重定向' : '并行对比'}</span>
            <button class="btn btn-sm btn-ghost" data-action="edit">编辑</button>
            <button class="btn btn-sm btn-ghost" data-action="toggle">
              ${rule.enabled ? '禁用' : '启用'}
            </button>
            <button class="btn btn-sm btn-ghost text-error" data-action="delete">删除</button>
          </div>
        </div>
        <div class="card-body">
          <div class="rule-details">
            <div class="detail-item">
              <span class="detail-label">URL模式:</span>
              <code>${rule.conditions.urlPattern}</code>
            </div>
            <div class="detail-item">
              <span class="detail-label">匹配方式:</span>
              <span>${this.getMatchTypeLabel(rule.conditions.urlMatchType)}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">目标URL:</span>
              <code>${rule.transformation.newUrl}</code>
            </div>
            ${rule.conditions.methods ? `
              <div class="detail-item">
                <span class="detail-label">HTTP方法:</span>
                <span>${rule.conditions.methods.join(', ')}</span>
              </div>
            ` : ''}
          </div>
        </div>
      </div>
    `).join('');
  }

  /**
   * 获取匹配类型标签
   */
  private getMatchTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'exact': '精确匹配',
      'contains': '包含',
      'startsWith': '开头匹配',
      'endsWith': '结尾匹配',
      'regex': '正则表达式'
    };
    return labels[type] || type;
  }

  /**
   * 初始化规则管理
   */
  private initializeRuleManagement(modal: HTMLElement): void {
    const addRuleBtn = modal.querySelector('#add-rule-btn') as HTMLButtonElement;
    const importBtn = modal.querySelector('#import-rules-btn') as HTMLButtonElement;
    const exportBtn = modal.querySelector('#export-rules-btn') as HTMLButtonElement;
    const rulesList = modal.querySelector('#rules-list') as HTMLElement;

    // 添加规则
    addRuleBtn?.addEventListener('click', () => {
      this.showRuleEditor();
    });

    // 导入规则
    importBtn?.addEventListener('click', () => {
      this.importRules();
    });

    // 导出规则
    exportBtn?.addEventListener('click', () => {
      this.exportRules();
    });

    // 规则操作事件委托
    rulesList?.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      const ruleCard = target.closest('.rule-card') as HTMLElement;
      const ruleId = ruleCard?.getAttribute('data-rule-id');

      if (!ruleId || !action) return;

      switch (action) {
        case 'edit':
          this.editRule(ruleId);
          break;
        case 'toggle':
          this.toggleRule(ruleId);
          break;
        case 'delete':
          this.deleteRule(ruleId);
          break;
      }
    });
  }

  /**
   * 初始化报告管理
   */
  private initializeReportManagement(modal: HTMLElement): void {
    const clearBtn = modal.querySelector('#clear-reports-btn') as HTMLButtonElement;
    const reportsList = modal.querySelector('#reports-list') as HTMLElement;

    // 清空报告
    clearBtn?.addEventListener('click', async () => {
      if (confirm('确定要清空所有对比报告吗？')) {
        this.reports = [];
        await this.saveData('reports', this.reports);
        this.renderReportsList(modal);
        this.updateReportsCount(modal);
        await this.showNotification('成功', '已清空所有报告');
      }
    });

    // 报告操作事件委托
    reportsList?.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      const reportCard = target.closest('.report-card') as HTMLElement;
      const reportId = reportCard?.getAttribute('data-report-id');

      if (!reportId || !action) return;

      switch (action) {
        case 'view':
          this.viewReport(reportId);
          break;
        case 'delete':
          this.deleteReport(reportId);
          break;
      }
    });
  }

  /**
   * 初始化设置管理
   */
  private initializeSettings(modal: HTMLElement): void {
    const maxReportsInput = modal.querySelector('#max-reports') as HTMLInputElement;
    const globalIgnoreTextarea = modal.querySelector('#global-ignore') as HTMLTextAreaElement;
    const autoStartCheckbox = modal.querySelector('#auto-start') as HTMLInputElement;

    // 加载设置
    this.loadSettings().then(settings => {
      if (maxReportsInput) maxReportsInput.value = settings.maxReports.toString();
      if (globalIgnoreTextarea) globalIgnoreTextarea.value = settings.globalIgnorePatterns.join('\n');
      if (autoStartCheckbox) autoStartCheckbox.checked = settings.autoStart;
    });

    // 保存设置
    const saveSettings = async () => {
      const settings = {
        maxReports: parseInt(maxReportsInput?.value || '100'),
        globalIgnorePatterns: globalIgnoreTextarea?.value.split('\n').filter(line => line.trim()) || [],
        autoStart: autoStartCheckbox?.checked || false
      };
      await this.saveData('settings', settings);
    };

    maxReportsInput?.addEventListener('change', saveSettings);
    globalIgnoreTextarea?.addEventListener('blur', saveSettings);
    autoStartCheckbox?.addEventListener('change', saveSettings);
  }

  /**
   * 初始化关闭事件
   */
  private initializeCloseEvents(modal: HTMLElement): void {
    const closeBtn = modal.querySelector('#close-btn') as HTMLButtonElement;
    const modalClose = modal.querySelector('.modal-close') as HTMLButtonElement;

    const closeModal = () => {
      modal.remove();
    };

    closeBtn?.addEventListener('click', closeModal);
    modalClose?.addEventListener('click', closeModal);

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
  }

  /**
   * 渲染报告列表
   */
  private renderReportsList(modal: HTMLElement): void {
    const reportsList = modal.querySelector('#reports-list') as HTMLElement;
    if (!reportsList) return;

    if (this.reports.length === 0) {
      reportsList.innerHTML = `
        <div class="empty-state">
          <p>暂无对比报告</p>
          <p class="text-muted">启动拦截后，匹配的API请求将生成对比报告</p>
        </div>
      `;
      return;
    }

    reportsList.innerHTML = this.reports.map(report => `
      <div class="card report-card" data-report-id="${report.id}">
        <div class="card-header">
          <div class="report-info">
            <h4 class="card-title">${report.ruleName}</h4>
            <p class="report-url">${report.request.url}</p>
          </div>
          <div class="report-actions">
            <span class="badge badge-${this.getSeverityColor(report.diff.severity)}">
              ${report.diff.changeCount} 处变更
            </span>
            <span class="report-time">${new Date(report.timestamp).toLocaleString()}</span>
            <button class="btn btn-sm btn-primary" data-action="view">查看</button>
            <button class="btn btn-sm btn-ghost text-error" data-action="delete">删除</button>
          </div>
        </div>
        <div class="card-body">
          <div class="report-summary">
            <div class="summary-item">
              <span class="summary-label">请求方法:</span>
              <span>${report.request.method}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">响应状态:</span>
              <span>旧: ${report.responses.old.status}, 新: ${report.responses.new.status}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">响应时间:</span>
              <span>旧: ${report.responses.old.responseTime}ms, 新: ${report.responses.new.responseTime}ms</span>
            </div>
          </div>
        </div>
      </div>
    `).join('');

    this.updateReportsCount(modal);
  }

  /**
   * 更新报告数量显示
   */
  private updateReportsCount(modal: HTMLElement): void {
    const countElement = modal.querySelector('#reports-count') as HTMLElement;
    if (countElement) {
      countElement.textContent = this.reports.length.toString();
    }
  }

  /**
   * 获取严重程度颜色
   */
  private getSeverityColor(severity: string): string {
    const colors: Record<string, string> = {
      'none': 'success',
      'minor': 'info',
      'major': 'warning',
      'critical': 'error'
    };
    return colors[severity] || 'secondary';
  }

  /**
   * 加载设置
   */
  private async loadSettings(): Promise<any> {
    return await this.loadData('settings', {
      maxReports: 100,
      globalIgnorePatterns: [],
      autoStart: false
    });
  }

  /**
   * 启动拦截器
   */
  private async startInterceptor(): Promise<void> {
    try {
      if (this.isIntercepting) return;

      // 动态导入依赖
      const { BatchInterceptor } = await import('@mswjs/interceptors');
      const { XMLHttpRequestInterceptor } = await import('@mswjs/interceptors/XMLHttpRequest');
      const { FetchInterceptor } = await import('@mswjs/interceptors/fetch');

      // 创建拦截器实例
      this.interceptor = new BatchInterceptor({
        name: 'api-migration-validator',
        interceptors: [
          new XMLHttpRequestInterceptor(),
          new FetchInterceptor()
        ],
      });

      // 设置事件监听
      this.interceptor.on('request', this.handleRequest.bind(this));

      // 应用拦截器
      this.interceptor.apply();
      this.isIntercepting = true;

      await this.showNotification('成功', 'API拦截器已启动');
      console.log('🔄 API拦截器已启动');

    } catch (error) {
      console.error('启动拦截器失败:', error);
      await this.showNotification('错误', '启动拦截器失败');
    }
  }

  /**
   * 停止拦截器
   */
  private async stopInterceptor(): Promise<void> {
    try {
      if (!this.isIntercepting || !this.interceptor) return;

      this.interceptor.dispose();
      this.interceptor = null;
      this.isIntercepting = false;

      await this.showNotification('成功', 'API拦截器已停止');
      console.log('⏹️ API拦截器已停止');

    } catch (error) {
      console.error('停止拦截器失败:', error);
      await this.showNotification('错误', '停止拦截器失败');
    }
  }

  /**
   * 显示规则编辑器
   */
  private showRuleEditor(rule?: InterceptRule): void {
    const modal = this.createRuleEditorModal(rule);
    document.body.appendChild(modal);

    // 初始化编辑器
    this.initializeRuleEditor(modal, rule);
  }

  /**
   * 创建规则编辑器模态框
   */
  private createRuleEditorModal(rule?: InterceptRule): HTMLElement {
    const isEdit = !!rule;
    const modal = document.createElement('div');
    modal.className = 'modal-overlay rule-editor-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">${isEdit ? '编辑' : '创建'}API拦截规则</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="rule-editor-container">
            <div class="tabs">
              <ul class="tabs-list">
                <li class="tabs-item">
                  <a href="#basic-info" class="tabs-link active">基础信息</a>
                </li>
                <li class="tabs-item">
                  <a href="#match-conditions" class="tabs-link">匹配条件</a>
                </li>
                <li class="tabs-item">
                  <a href="#transformation" class="tabs-link">转换规则</a>
                </li>
                <li class="tabs-item">
                  <a href="#diff-settings" class="tabs-link">对比设置</a>
                </li>
              </ul>
            </div>

            <div class="tabs-content">
              <!-- 基础信息 -->
              <div id="basic-info" class="tab-pane active">
                ${this.createBasicInfoForm(rule)}
              </div>

              <!-- 匹配条件 -->
              <div id="match-conditions" class="tab-pane">
                ${this.createMatchConditionsForm(rule)}
              </div>

              <!-- 转换规则 -->
              <div id="transformation" class="tab-pane">
                ${this.createTransformationForm(rule)}
              </div>

              <!-- 对比设置 -->
              <div id="diff-settings" class="tab-pane">
                ${this.createDiffSettingsForm(rule)}
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="cancel-rule-btn">取消</button>
          <button class="btn btn-outline" id="test-rule-btn">测试规则</button>
          <button class="btn btn-primary" id="save-rule-btn">${isEdit ? '更新' : '创建'}规则</button>
        </div>
      </div>
    `;

    return modal;
  }

  /**
   * 创建基础信息表单
   */
  private createBasicInfoForm(rule?: InterceptRule): string {
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">规则名称 *</label>
          <input type="text" class="form-control" id="rule-name"
            placeholder="输入规则名称" value="${rule?.name || ''}" required>
        </div>

        <div class="form-group">
          <label class="form-label">规则描述</label>
          <textarea class="form-control form-textarea" id="rule-description"
            placeholder="描述此规则的用途和场景">${rule?.description || ''}</textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">优先级</label>
            <input type="number" class="form-control" id="rule-priority"
              value="${rule?.priority || 100}" min="0" max="999">
            <small class="form-text text-muted">数字越大优先级越高</small>
          </div>

          <div class="form-group">
            <label class="form-label">执行模式</label>
            <select class="form-control form-select" id="execution-mode">
              <option value="parallel" ${rule?.mode === 'parallel' ? 'selected' : ''}>
                并行对比 - 同时调用新老接口进行对比
              </option>
              <option value="redirect" ${rule?.mode === 'redirect' ? 'selected' : ''}>
                重定向替换 - 直接替换为新接口
              </option>
            </select>
          </div>
        </div>

        <div class="form-check">
          <input type="checkbox" class="form-check-input" id="rule-enabled"
            ${rule?.enabled !== false ? 'checked' : ''}>
          <label class="form-check-label" for="rule-enabled">启用此规则</label>
        </div>
      </div>
    `;
  }

  /**
   * 创建匹配条件表单
   */
  private createMatchConditionsForm(rule?: InterceptRule): string {
    const conditions = rule?.conditions;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">URL匹配模式 *</label>
          <input type="text" class="form-control" id="url-pattern"
            placeholder="例如: /api/v1/users 或 https://api.example.com"
            value="${conditions?.urlPattern || ''}" required>
        </div>

        <div class="form-group">
          <label class="form-label">匹配方式</label>
          <select class="form-control form-select" id="url-match-type">
            <option value="contains" ${conditions?.urlMatchType === 'contains' ? 'selected' : ''}>
              包含 - URL包含指定字符串
            </option>
            <option value="startsWith" ${conditions?.urlMatchType === 'startsWith' ? 'selected' : ''}>
              开头匹配 - URL以指定字符串开头
            </option>
            <option value="endsWith" ${conditions?.urlMatchType === 'endsWith' ? 'selected' : ''}>
              结尾匹配 - URL以指定字符串结尾
            </option>
            <option value="exact" ${conditions?.urlMatchType === 'exact' ? 'selected' : ''}>
              精确匹配 - URL完全相等
            </option>
            <option value="regex" ${conditions?.urlMatchType === 'regex' ? 'selected' : ''}>
              正则表达式 - 使用正则表达式匹配
            </option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">HTTP方法过滤</label>
          <div class="form-check-group">
            ${['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'].map(method => `
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="method-${method}"
                  value="${method}" ${conditions?.methods?.includes(method) ? 'checked' : ''}>
                <label class="form-check-label" for="method-${method}">${method}</label>
              </div>
            `).join('')}
          </div>
          <small class="form-text text-muted">不选择则匹配所有HTTP方法</small>
        </div>

        <div class="advanced-config" id="advanced-conditions">
          <div class="advanced-config-header">
            <span>高级匹配条件</span>
            <button type="button" class="advanced-config-toggle">▼</button>
          </div>
          <div class="advanced-config-content">
            <div class="form-group">
              <label class="form-label">请求头条件</label>
              <textarea class="form-control form-textarea" id="header-conditions"
                placeholder="JSON格式，例如: {&quot;Content-Type&quot;: &quot;application/json&quot;}">${
                  conditions?.headers ? JSON.stringify(conditions.headers, null, 2) : ''
                }</textarea>
              <small class="form-text text-muted">支持字符串精确匹配和正则表达式</small>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 创建转换规则表单
   */
  private createTransformationForm(rule?: InterceptRule): string {
    const transformation = rule?.transformation;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">新接口URL *</label>
          <input type="text" class="form-control" id="new-url"
            placeholder="例如: https://api-v2.example.com/users"
            value="${transformation?.newUrl || ''}" required>
          <small class="form-text text-muted">支持变量替换，如 {id}, {userId} 等</small>
        </div>

        <div class="form-group">
          <label class="form-label">参数映射</label>
          <textarea class="form-control form-textarea" id="param-mapping"
            placeholder="JSON格式，例如: {&quot;old_param&quot;: &quot;new_param&quot;}">${
              transformation?.paramMapping ? JSON.stringify(transformation.paramMapping, null, 2) : ''
            }</textarea>
          <small class="form-text text-muted">将旧参数名映射到新参数名</small>
        </div>

        <div class="form-group">
          <label class="form-label">请求头映射</label>
          <textarea class="form-control form-textarea" id="header-mapping"
            placeholder="JSON格式，例如: {&quot;X-Old-Token&quot;: &quot;Authorization&quot;}">${
              transformation?.headerMapping ? JSON.stringify(transformation.headerMapping, null, 2) : ''
            }</textarea>
          <small class="form-text text-muted">将旧请求头映射到新请求头</small>
        </div>

        <div class="form-check">
          <input type="checkbox" class="form-check-input" id="preserve-params"
            ${transformation?.preserveOriginalParams ? 'checked' : ''}>
          <label class="form-check-label" for="preserve-params">保留原始参数</label>
          <small class="form-text text-muted">在映射的基础上保留未映射的原始参数</small>
        </div>
      </div>
    `;
  }

  /**
   * 创建对比设置表单
   */
  private createDiffSettingsForm(rule?: InterceptRule): string {
    const diffConfig = rule?.diffConfig;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">忽略字段</label>
          <textarea class="form-control form-textarea" id="ignore-fields"
            placeholder="每行一个字段路径，例如:&#10;timestamp&#10;data.id&#10;meta.version">${
              diffConfig?.ignoreFields?.join('\n') || ''
            }</textarea>
          <small class="form-text text-muted">支持点号路径，如 data.user.id</small>
        </div>

        <div class="form-row">
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="case-sensitive"
              ${diffConfig?.caseSensitive !== false ? 'checked' : ''}>
            <label class="form-check-label" for="case-sensitive">大小写敏感</label>
          </div>

          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="array-order-sensitive"
              ${diffConfig?.arrayOrderSensitive !== false ? 'checked' : ''}>
            <label class="form-check-label" for="array-order-sensitive">数组顺序敏感</label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">数值容差</label>
          <input type="number" class="form-control" id="numeric-tolerance"
            value="${diffConfig?.numericTolerance || 0}" min="0" step="0.001">
          <small class="form-text text-muted">数值比较的容差范围，0表示精确匹配</small>
        </div>
      </div>
    `;
  }

  /**
   * 初始化规则编辑器
   */
  private initializeRuleEditor(modal: HTMLElement, rule?: InterceptRule): void {
    const isEdit = !!rule;

    // 初始化标签页切换
    this.initializeTabs(modal);

    // 初始化高级配置折叠
    this.initializeAdvancedConfig(modal);

    // 初始化表单验证
    this.initializeFormValidation(modal);

    // 绑定按钮事件
    const cancelBtn = modal.querySelector('#cancel-rule-btn') as HTMLButtonElement;
    const testBtn = modal.querySelector('#test-rule-btn') as HTMLButtonElement;
    const saveBtn = modal.querySelector('#save-rule-btn') as HTMLButtonElement;
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;

    // 取消/关闭
    const closeModal = () => modal.remove();
    cancelBtn?.addEventListener('click', closeModal);
    closeBtn?.addEventListener('click', closeModal);

    // 测试规则
    testBtn?.addEventListener('click', () => {
      this.testRule(modal);
    });

    // 保存规则
    saveBtn?.addEventListener('click', async () => {
      const ruleData = this.extractRuleData(modal);
      if (ruleData) {
        if (isEdit) {
          await this.updateRule(rule!.id, ruleData);
        } else {
          await this.createRule(ruleData);
        }
        closeModal();
      }
    });

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });
  }

  /**
   * 初始化高级配置折叠
   */
  private initializeAdvancedConfig(modal: HTMLElement): void {
    const advancedConfigs = modal.querySelectorAll('.advanced-config');

    advancedConfigs.forEach(config => {
      const header = config.querySelector('.advanced-config-header') as HTMLElement;

      header?.addEventListener('click', () => {
        config.classList.toggle('expanded');
      });
    });
  }

  /**
   * 初始化表单验证
   */
  private initializeFormValidation(modal: HTMLElement): void {
    const requiredFields = modal.querySelectorAll('input[required], textarea[required]');

    requiredFields.forEach(field => {
      field.addEventListener('blur', () => {
        this.validateField(field as HTMLInputElement);
      });

      field.addEventListener('input', () => {
        this.clearFieldError(field as HTMLInputElement);
      });
    });
  }

  /**
   * 验证单个字段
   */
  private validateField(field: HTMLInputElement): boolean {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // 必填验证
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = '此字段为必填项';
    }

    // URL格式验证
    if (field.id === 'new-url' && value) {
      try {
        new URL(value);
      } catch {
        if (!value.startsWith('/')) {
          isValid = false;
          errorMessage = '请输入有效的URL或路径';
        }
      }
    }

    // JSON格式验证
    if (['header-conditions', 'param-mapping', 'header-mapping'].includes(field.id) && value) {
      try {
        JSON.parse(value);
      } catch {
        isValid = false;
        errorMessage = '请输入有效的JSON格式';
      }
    }

    // 正则表达式验证
    if (field.id === 'url-pattern') {
      const modalElement = field.closest('.modal') as HTMLElement;
      const matchType = (modalElement?.querySelector('#url-match-type') as HTMLSelectElement)?.value;
      if (matchType === 'regex' && value) {
        try {
          new RegExp(value);
        } catch {
          isValid = false;
          errorMessage = '请输入有效的正则表达式';
        }
      }
    }

    this.setFieldValidation(field, isValid, errorMessage);
    return isValid;
  }

  /**
   * 设置字段验证状态
   */
  private setFieldValidation(field: HTMLInputElement, isValid: boolean, errorMessage: string): void {
    const formGroup = field.closest('.form-group') as HTMLElement;
    let feedback = formGroup?.querySelector('.form-feedback') as HTMLElement;

    // 移除现有状态
    field.classList.remove('is-valid', 'is-invalid');

    if (feedback) {
      feedback.remove();
    }

    if (!isValid) {
      field.classList.add('is-invalid');
      feedback = document.createElement('div');
      feedback.className = 'form-feedback invalid-feedback';
      feedback.textContent = errorMessage;
      formGroup?.appendChild(feedback);
    } else if (field.value.trim()) {
      field.classList.add('is-valid');
    }
  }

  /**
   * 清除字段错误状态
   */
  private clearFieldError(field: HTMLInputElement): void {
    field.classList.remove('is-invalid');
    const formGroup = field.closest('.form-group') as HTMLElement;
    const feedback = formGroup?.querySelector('.invalid-feedback');
    feedback?.remove();
  }

  /**
   * 测试规则
   */
  private testRule(modal: HTMLElement): void {
    const ruleData = this.extractRuleData(modal, false);
    if (!ruleData) return;

    // 创建测试界面
    const testModal = this.createTestModal(ruleData);
    document.body.appendChild(testModal);
  }

  /**
   * 创建测试模态框
   */
  private createTestModal(rule: Partial<InterceptRule>): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">测试规则: ${rule.name}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">测试URL</label>
            <input type="text" class="form-control" id="test-url"
              placeholder="输入要测试的URL" value="https://api.example.com/v1/users">
          </div>
          <div class="form-group">
            <label class="form-label">HTTP方法</label>
            <select class="form-control form-select" id="test-method">
              <option value="GET">GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="DELETE">DELETE</option>
            </select>
          </div>
          <div class="test-result" id="test-result" style="display: none;">
            <h4>测试结果</h4>
            <div class="test-output"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">关闭</button>
          <button class="btn btn-primary" id="run-test-btn">运行测试</button>
        </div>
      </div>
    `;

    // 绑定测试事件
    const runTestBtn = modal.querySelector('#run-test-btn') as HTMLButtonElement;
    runTestBtn?.addEventListener('click', () => {
      this.runRuleTest(modal, rule);
    });

    return modal;
  }

  /**
   * 运行规则测试
   */
  private runRuleTest(modal: HTMLElement, rule: Partial<InterceptRule>): void {
    const testUrl = (modal.querySelector('#test-url') as HTMLInputElement)?.value;
    const testMethod = (modal.querySelector('#test-method') as HTMLSelectElement)?.value;
    const resultDiv = modal.querySelector('#test-result') as HTMLElement;
    const outputDiv = modal.querySelector('.test-output') as HTMLElement;

    if (!testUrl) {
      outputDiv.innerHTML = '<div class="alert alert-error">请输入测试URL</div>';
      resultDiv.style.display = 'block';
      return;
    }

    // 模拟规则匹配测试
    const isMatched = this.testRuleMatch(rule, testUrl, testMethod);
    const transformedUrl = isMatched ? this.testUrlTransformation(rule, testUrl) : null;

    outputDiv.innerHTML = `
      <div class="test-results">
        <div class="result-item">
          <span class="result-label">匹配结果:</span>
          <span class="badge badge-${isMatched ? 'success' : 'error'}">
            ${isMatched ? '匹配' : '不匹配'}
          </span>
        </div>
        ${isMatched ? `
          <div class="result-item">
            <span class="result-label">原始URL:</span>
            <code>${testUrl}</code>
          </div>
          <div class="result-item">
            <span class="result-label">转换后URL:</span>
            <code>${transformedUrl}</code>
          </div>
          <div class="result-item">
            <span class="result-label">执行模式:</span>
            <span class="badge badge-info">${rule.mode === 'redirect' ? '重定向' : '并行对比'}</span>
          </div>
        ` : `
          <div class="result-item">
            <span class="result-label">原因:</span>
            <span class="text-muted">URL模式不匹配或HTTP方法不符合条件</span>
          </div>
        `}
      </div>
    `;

    resultDiv.style.display = 'block';
  }

  /**
   * 测试规则匹配
   */
  private testRuleMatch(rule: Partial<InterceptRule>, url: string, method: string): boolean {
    const conditions = rule.conditions;
    if (!conditions) return false;

    // URL匹配测试
    const { urlPattern, urlMatchType } = conditions;
    let urlMatched = false;

    switch (urlMatchType) {
      case 'exact':
        urlMatched = url === urlPattern;
        break;
      case 'contains':
        urlMatched = url.includes(urlPattern);
        break;
      case 'startsWith':
        urlMatched = url.startsWith(urlPattern);
        break;
      case 'endsWith':
        urlMatched = url.endsWith(urlPattern);
        break;
      case 'regex':
        try {
          urlMatched = new RegExp(urlPattern).test(url);
        } catch {
          urlMatched = false;
        }
        break;
      default:
        urlMatched = url.includes(urlPattern);
    }

    if (!urlMatched) return false;

    // HTTP方法测试
    if (conditions.methods && conditions.methods.length > 0) {
      return conditions.methods.includes(method.toUpperCase());
    }

    return true;
  }

  /**
   * 测试URL转换
   */
  private testUrlTransformation(rule: Partial<InterceptRule>, originalUrl: string): string {
    const transformation = rule.transformation;
    if (!transformation) return originalUrl;

    let newUrl = transformation.newUrl;

    // 简单的参数映射测试
    if (transformation.paramMapping) {
      try {
        const urlObj = new URL(originalUrl);
        const newUrlObj = new URL(newUrl);

        for (const [oldParam, newParam] of Object.entries(transformation.paramMapping)) {
          const value = urlObj.searchParams.get(oldParam);
          if (value) {
            newUrlObj.searchParams.set(newParam, value);
          }
        }

        if (transformation.preserveOriginalParams) {
          urlObj.searchParams.forEach((value, key) => {
            if (!transformation.paramMapping![key] && !newUrlObj.searchParams.has(key)) {
              newUrlObj.searchParams.set(key, value);
            }
          });
        }

        newUrl = newUrlObj.toString();
      } catch (error) {
        console.warn('URL transformation test failed:', error);
      }
    }

    return newUrl;
  }

  /**
   * 提取规则数据
   */
  private extractRuleData(modal: HTMLElement, validate: boolean = true): Partial<InterceptRule> | null {
    // 基础信息
    const name = (modal.querySelector('#rule-name') as HTMLInputElement)?.value.trim();
    const description = (modal.querySelector('#rule-description') as HTMLTextAreaElement)?.value.trim();
    const priority = parseInt((modal.querySelector('#rule-priority') as HTMLInputElement)?.value || '100');
    const mode = (modal.querySelector('#execution-mode') as HTMLSelectElement)?.value as 'redirect' | 'parallel';
    const enabled = (modal.querySelector('#rule-enabled') as HTMLInputElement)?.checked;

    // 匹配条件
    const urlPattern = (modal.querySelector('#url-pattern') as HTMLInputElement)?.value.trim();
    const urlMatchType = (modal.querySelector('#url-match-type') as HTMLSelectElement)?.value as any;

    const methodCheckboxes = modal.querySelectorAll('input[type="checkbox"][id^="method-"]:checked');
    const methods = Array.from(methodCheckboxes).map(cb => (cb as HTMLInputElement).value);

    const headerConditionsText = (modal.querySelector('#header-conditions') as HTMLTextAreaElement)?.value.trim();
    let headers: Record<string, string> | undefined;

    if (headerConditionsText) {
      try {
        headers = JSON.parse(headerConditionsText);
      } catch (error) {
        if (validate) {
          this.showNotification('错误', '请求头条件JSON格式错误');
          return null;
        }
      }
    }

    // 转换规则
    const newUrl = (modal.querySelector('#new-url') as HTMLInputElement)?.value.trim();

    const paramMappingText = (modal.querySelector('#param-mapping') as HTMLTextAreaElement)?.value.trim();
    let paramMapping: Record<string, string> | undefined;

    if (paramMappingText) {
      try {
        paramMapping = JSON.parse(paramMappingText);
      } catch (error) {
        if (validate) {
          this.showNotification('错误', '参数映射JSON格式错误');
          return null;
        }
      }
    }

    const headerMappingText = (modal.querySelector('#header-mapping') as HTMLTextAreaElement)?.value.trim();
    let headerMapping: Record<string, string> | undefined;

    if (headerMappingText) {
      try {
        headerMapping = JSON.parse(headerMappingText);
      } catch (error) {
        if (validate) {
          this.showNotification('错误', '请求头映射JSON格式错误');
          return null;
        }
      }
    }

    const preserveOriginalParams = (modal.querySelector('#preserve-params') as HTMLInputElement)?.checked;

    // 对比设置
    const ignoreFieldsText = (modal.querySelector('#ignore-fields') as HTMLTextAreaElement)?.value.trim();
    const ignoreFields = ignoreFieldsText ? ignoreFieldsText.split('\n').filter(line => line.trim()) : undefined;

    const caseSensitive = (modal.querySelector('#case-sensitive') as HTMLInputElement)?.checked;
    const arrayOrderSensitive = (modal.querySelector('#array-order-sensitive') as HTMLInputElement)?.checked;
    const numericTolerance = parseFloat((modal.querySelector('#numeric-tolerance') as HTMLInputElement)?.value || '0');

    // 验证必填字段
    if (validate) {
      if (!name) {
        this.showNotification('错误', '规则名称不能为空');
        return null;
      }
      if (!urlPattern) {
        this.showNotification('错误', 'URL匹配模式不能为空');
        return null;
      }
      if (!newUrl) {
        this.showNotification('错误', '新接口URL不能为空');
        return null;
      }
    }

    return {
      name,
      description: description || undefined,
      priority,
      mode,
      enabled: enabled !== false,
      conditions: {
        urlPattern,
        urlMatchType,
        methods: methods.length > 0 ? methods : undefined,
        headers
      },
      transformation: {
        newUrl,
        paramMapping,
        headerMapping,
        preserveOriginalParams
      },
      diffConfig: {
        ignoreFields,
        caseSensitive,
        arrayOrderSensitive,
        numericTolerance: numericTolerance > 0 ? numericTolerance : undefined
      }
    };
  }

  /**
   * 创建新规则
   */
  private async createRule(ruleData: Partial<InterceptRule>): Promise<void> {
    const rule: InterceptRule = {
      id: this.generateId(),
      name: ruleData.name!,
      description: ruleData.description,
      enabled: ruleData.enabled !== false,
      priority: ruleData.priority || 100,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      conditions: ruleData.conditions!,
      transformation: ruleData.transformation!,
      mode: ruleData.mode!,
      diffConfig: ruleData.diffConfig
    };

    this.rules.push(rule);
    await this.saveRules();

    // 刷新主界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    await this.showNotification('成功', `规则 "${rule.name}" 创建成功`);
  }

  /**
   * 更新规则
   */
  private async updateRule(ruleId: string, ruleData: Partial<InterceptRule>): Promise<void> {
    const ruleIndex = this.rules.findIndex(r => r.id === ruleId);
    if (ruleIndex === -1) {
      await this.showNotification('错误', '规则不存在');
      return;
    }

    const existingRule = this.rules[ruleIndex];
    const updatedRule: InterceptRule = {
      ...existingRule,
      name: ruleData.name!,
      description: ruleData.description,
      enabled: ruleData.enabled !== false,
      priority: ruleData.priority || 100,
      updatedAt: Date.now(),
      conditions: ruleData.conditions!,
      transformation: ruleData.transformation!,
      mode: ruleData.mode!,
      diffConfig: ruleData.diffConfig
    };

    this.rules[ruleIndex] = updatedRule;
    await this.saveRules();

    // 刷新主界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    await this.showNotification('成功', `规则 "${updatedRule.name}" 更新成功`);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return 'rule_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * 导入规则
   */
  private importRules(): void {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        const data = JSON.parse(text);

        if (Array.isArray(data)) {
          // 直接导入规则数组
          this.rules = [...this.rules, ...data];
        } else if (data.rules && Array.isArray(data.rules)) {
          // 导入完整的导出数据
          this.rules = [...this.rules, ...data.rules];
        } else {
          throw new Error('无效的文件格式');
        }

        await this.saveRules();

        // 刷新界面
        const mainModal = document.querySelector('.api-migration-validator-modal');
        if (mainModal) {
          this.renderRulesList(mainModal as HTMLElement);
        }

        await this.showNotification('成功', `成功导入 ${data.length || data.rules?.length || 0} 条规则`);
      } catch (error) {
        await this.showNotification('错误', '导入失败：' + (error as Error).message);
      }
    };
    input.click();
  }

  /**
   * 导出规则
   */
  private exportRules(): void {
    const exportData = {
      version: '1.0.0',
      exportTime: Date.now(),
      rules: this.rules,
      config: {
        toolVersion: this.version
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `api-migration-rules-${new Date().toISOString().split('T')[0]}.json`;
    a.click();

    URL.revokeObjectURL(url);
  }

  /**
   * 编辑规则
   */
  private editRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) {
      this.showNotification('错误', '规则不存在');
      return;
    }
    this.showRuleEditor(rule);
  }

  /**
   * 切换规则状态
   */
  private toggleRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) return;

    rule.enabled = !rule.enabled;
    rule.updatedAt = Date.now();

    this.saveRules();

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    this.showNotification('成功', `规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`);
  }

  /**
   * 删除规则
   */
  private deleteRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) return;

    if (!confirm(`确定要删除规则 "${rule.name}" 吗？此操作不可撤销。`)) {
      return;
    }

    this.rules = this.rules.filter(r => r.id !== ruleId);
    this.saveRules();

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    this.showNotification('成功', `规则 "${rule.name}" 已删除`);
  }

  /**
   * 查看报告
   */
  private viewReport(reportId: string): void {
    const report = this.reports.find(r => r.id === reportId);
    if (!report) {
      this.showNotification('错误', '报告不存在');
      return;
    }

    this.showDiffViewer(report);
  }

  /**
   * 删除报告
   */
  private deleteReport(reportId: string): void {
    const report = this.reports.find(r => r.id === reportId);
    if (!report) return;

    if (!confirm('确定要删除此报告吗？')) {
      return;
    }

    this.reports = this.reports.filter(r => r.id !== reportId);
    this.saveData('reports', this.reports);

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderReportsList(mainModal as HTMLElement);
    }

    this.showNotification('成功', '报告已删除');
  }

  /**
   * 显示差异查看器
   */
  private showDiffViewer(report: DiffReport): void {
    const modal = this.createDiffViewerModal(report);
    document.body.appendChild(modal);
  }

  /**
   * 创建差异查看器模态框
   */
  private createDiffViewerModal(report: DiffReport): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay diff-viewer-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">API对比结果 - ${report.ruleName}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="diff-header">
            <div class="diff-summary">
              <span class="badge badge-${this.getSeverityColor(report.diff.severity)}">
                ${report.diff.changeCount} 处变更
              </span>
              <span class="diff-meta">
                响应时间: 旧接口 ${report.responses.old.responseTime}ms,
                新接口 ${report.responses.new.responseTime}ms
              </span>
            </div>
          </div>

          <div class="tabs">
            <ul class="tabs-list">
              <li class="tabs-item">
                <a href="#visual-diff" class="tabs-link active">可视化差异</a>
              </li>
              <li class="tabs-item">
                <a href="#side-by-side" class="tabs-link">并排对比</a>
              </li>
              <li class="tabs-item">
                <a href="#raw-data" class="tabs-link">原始数据</a>
              </li>
            </ul>
          </div>

          <div class="tabs-content">
            <div id="visual-diff" class="tab-pane active">
              <div class="diff-html-container">
                ${report.visualizations.html}
              </div>
            </div>

            <div id="side-by-side" class="tab-pane">
              <div class="side-by-side-container">
                <div class="response-panel">
                  <h4>旧接口响应</h4>
                  <pre><code>${JSON.stringify(report.responses.old.body, null, 2)}</code></pre>
                </div>
                <div class="response-panel">
                  <h4>新接口响应</h4>
                  <pre><code>${JSON.stringify(report.responses.new.body, null, 2)}</code></pre>
                </div>
              </div>
            </div>

            <div id="raw-data" class="tab-pane">
              <div class="raw-data-container">
                <h4>请求信息</h4>
                <pre><code>${JSON.stringify(report.request, null, 2)}</code></pre>

                <h4>差异Delta</h4>
                <pre><code>${JSON.stringify(report.diff.delta, null, 2)}</code></pre>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">关闭</button>
          <button class="btn btn-outline" onclick="navigator.clipboard.writeText(JSON.stringify(${JSON.stringify(report)}, null, 2))">复制报告</button>
        </div>
      </div>
    `;

    // 初始化标签页
    this.initializeTabs(modal);

    return modal;
  }



  /**
   * 核心请求处理逻辑
   */
  private async handleRequest(params: any): Promise<void> {
    const { url, method, headers } = params;

    // 查找匹配的规则
    const matchedRule = this.findMatchingRule(url, method, headers);
    if (!matchedRule) return;

    try {
      if (matchedRule.mode === 'redirect') {
        // 重定向模式：直接替换请求
        await this.redirectRequest(params, matchedRule);
      } else {
        // 并行对比模式：同时调用新老接口
        await this.parallelCompare(params, matchedRule);
      }
    } catch (error) {
      console.error('Request handling failed:', error);
      await this.showNotification('错误', '请求处理失败：' + (error as Error).message);
    }
  }

  /**
   * 查找匹配的规则
   */
  private findMatchingRule(url: string, method: string, headers: Record<string, string>): InterceptRule | null {
    // 按优先级排序
    const sortedRules = this.rules
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (this.isRuleMatched(rule, url, method, headers)) {
        return rule;
      }
    }

    return null;
  }

  /**
   * 检查规则是否匹配
   */
  private isRuleMatched(rule: InterceptRule, url: string, method: string, headers: Record<string, string>): boolean {
    const { conditions } = rule;

    // URL匹配
    if (!this.isUrlMatched(conditions.urlPattern, conditions.urlMatchType, url)) {
      return false;
    }

    // HTTP方法匹配
    if (conditions.methods && conditions.methods.length > 0) {
      if (!conditions.methods.includes(method.toUpperCase())) {
        return false;
      }
    }

    // 请求头匹配
    if (conditions.headers) {
      for (const [headerName, expectedValue] of Object.entries(conditions.headers)) {
        const actualValue = headers[headerName] || headers[headerName.toLowerCase()];
        if (!actualValue || !this.isHeaderValueMatched(expectedValue, actualValue)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * URL匹配检查
   */
  private isUrlMatched(pattern: string, matchType: string, url: string): boolean {
    switch (matchType) {
      case 'exact':
        return url === pattern;
      case 'contains':
        return url.includes(pattern);
      case 'startsWith':
        return url.startsWith(pattern);
      case 'endsWith':
        return url.endsWith(pattern);
      case 'regex':
        try {
          return new RegExp(pattern).test(url);
        } catch {
          return false;
        }
      default:
        return url.includes(pattern);
    }
  }

  /**
   * 请求头值匹配检查
   */
  private isHeaderValueMatched(expected: string, actual: string): boolean {
    // 支持正则表达式匹配
    if (expected.startsWith('/') && expected.endsWith('/')) {
      try {
        const regex = new RegExp(expected.slice(1, -1));
        return regex.test(actual);
      } catch {
        return false;
      }
    }

    // 精确匹配
    return expected === actual;
  }

  /**
   * 重定向请求
   */
  private async redirectRequest(params: any, rule: InterceptRule): Promise<void> {
    const transformedRequest = this.transformRequest(params, rule);

    // 这里应该通过拦截器重定向请求
    // 实际实现需要与@mswjs/interceptors集成
    console.log('Redirecting request:', {
      original: params,
      transformed: transformedRequest,
      rule: rule.name
    });
  }

  /**
   * 并行对比
   */
  private async parallelCompare(params: any, rule: InterceptRule): Promise<void> {
    const transformedRequest = this.transformRequest(params, rule);

    try {
      // 并行调用新老接口
      const [oldResponse, newResponse] = await Promise.all([
        this.makeRequest(params),
        this.makeRequest(transformedRequest)
      ]);

      // 生成对比报告
      const report = await this.generateDiffReport(params, rule, oldResponse, newResponse);

      // 保存报告
      this.reports.unshift(report);
      await this.saveData('reports', this.reports);

      // 通知用户
      await this.showNotification('对比完成', `规则 "${rule.name}" 执行完成，发现 ${report.diff.changeCount} 处差异`);

    } catch (error) {
      console.error('Parallel comparison failed:', error);
      await this.showNotification('错误', '并行对比失败：' + (error as Error).message);
    }
  }

  /**
   * 转换请求
   */
  private transformRequest(originalRequest: any, rule: InterceptRule): any {
    const { transformation } = rule;
    const transformed = { ...originalRequest };

    // URL转换
    transformed.url = transformation.newUrl;

    // 参数映射
    if (transformation.paramMapping && originalRequest.params) {
      const newParams: Record<string, any> = {};

      // 映射参数
      for (const [oldKey, newKey] of Object.entries(transformation.paramMapping)) {
        if (originalRequest.params[oldKey] !== undefined) {
          newParams[newKey] = originalRequest.params[oldKey];
        }
      }

      // 保留原始参数
      if (transformation.preserveOriginalParams) {
        for (const [key, value] of Object.entries(originalRequest.params)) {
          if (!transformation.paramMapping[key] && newParams[key] === undefined) {
            newParams[key] = value;
          }
        }
      }

      transformed.params = newParams;
    }

    // 请求头映射
    if (transformation.headerMapping && originalRequest.headers) {
      const newHeaders: Record<string, string> = { ...originalRequest.headers };

      for (const [oldHeader, newHeader] of Object.entries(transformation.headerMapping)) {
        if (originalRequest.headers[oldHeader]) {
          newHeaders[newHeader] = originalRequest.headers[oldHeader];
          delete newHeaders[oldHeader];
        }
      }

      transformed.headers = newHeaders;
    }

    return transformed;
  }

  /**
   * 发起请求
   */
  private async makeRequest(requestParams: any): Promise<any> {
    const startTime = Date.now();

    try {
      const response = await fetch(requestParams.url, {
        method: requestParams.method,
        headers: requestParams.headers,
        body: requestParams.body
      });

      const responseTime = Date.now() - startTime;
      const body = await response.json();

      return {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body,
        responseTime
      };
    } catch (error) {
      return {
        status: 0,
        statusText: 'Network Error',
        headers: {},
        body: null,
        responseTime: Date.now() - startTime,
        error: (error as Error).message
      };
    }
  }

  /**
   * 生成差异报告
   */
  private async generateDiffReport(
    request: any,
    rule: InterceptRule,
    oldResponse: any,
    newResponse: any
  ): Promise<DiffReport> {
    // 使用jsondiffpatch进行对比
    const delta = (window as any).jsondiffpatch?.diff(oldResponse.body, newResponse.body);

    // 计算变更数量
    const changeCount = this.countChanges(delta);

    // 确定严重程度
    const severity = this.determineSeverity(changeCount, oldResponse, newResponse);

    // 生成HTML可视化
    const html = this.generateDiffHtml(delta, oldResponse.body, newResponse.body);

    const report: DiffReport = {
      id: this.generateId(),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: request.url,
        method: request.method,
        headers: request.headers,
        body: request.body
      },
      responses: {
        old: oldResponse,
        new: newResponse
      },
      diff: {
        delta,
        hasChanges: changeCount > 0,
        changeCount,
        severity
      },
      visualizations: {
        html,
        summary: this.generateDiffSummary(delta)
      }
    };

    return report;
  }

  /**
   * 计算变更数量
   */
  private countChanges(delta: any): number {
    if (!delta) return 0;

    let count = 0;
    const traverse = (obj: any) => {
      if (typeof obj === 'object' && obj !== null) {
        for (const key in obj) {
          if (Array.isArray(obj[key]) && obj[key].length === 2) {
            count++; // 值变更
          } else if (Array.isArray(obj[key]) && obj[key].length === 1) {
            count++; // 删除
          } else if (Array.isArray(obj[key]) && obj[key].length === 3 && obj[key][2] === 0) {
            count++; // 添加
          } else {
            traverse(obj[key]);
          }
        }
      }
    };

    traverse(delta);
    return count;
  }

  /**
   * 确定严重程度
   */
  private determineSeverity(changeCount: number, oldResponse: any, newResponse: any): 'none' | 'minor' | 'major' | 'critical' {
    // 状态码不同
    if (oldResponse.status !== newResponse.status) {
      return 'critical';
    }

    // 变更数量判断
    if (changeCount === 0) return 'none';
    if (changeCount <= 5) return 'minor';
    if (changeCount <= 20) return 'major';
    return 'critical';
  }

  /**
   * 生成差异HTML
   */
  private generateDiffHtml(delta: any, oldData: any, _newData: any): string {
    if (!(window as any).jsondiffpatch) {
      return '<div class="alert alert-warning">jsondiffpatch库未加载，无法显示可视化差异</div>';
    }

    try {
      return (window as any).jsondiffpatch.formatters.html.format(delta, oldData);
    } catch (error) {
      return `<div class="alert alert-error">生成差异视图失败：${(error as Error).message}</div>`;
    }
  }

  /**
   * 生成差异摘要
   */
  private generateDiffSummary(delta: any): string {
    if (!delta) return '无差异';

    const changes: string[] = [];
    const traverse = (obj: any, path: string = '') => {
      if (typeof obj === 'object' && obj !== null) {
        for (const key in obj) {
          const currentPath = path ? `${path}.${key}` : key;

          if (Array.isArray(obj[key])) {
            if (obj[key].length === 2) {
              changes.push(`修改: ${currentPath}`);
            } else if (obj[key].length === 1) {
              changes.push(`删除: ${currentPath}`);
            } else if (obj[key].length === 3 && obj[key][2] === 0) {
              changes.push(`添加: ${currentPath}`);
            }
          } else {
            traverse(obj[key], currentPath);
          }
        }
      }
    };

    traverse(delta);
    return changes.slice(0, 10).join(', ') + (changes.length > 10 ? '...' : '');
  }
}
